apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper
  labels:
    app: zookeeper
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.4.0
        ports:
        - containerPort: 2181
        env:
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ER_CLIENT_PORT
          value: "2181"
        - name: <PERSON><PERSON><PERSON><PERSON>PER_TICK_TIME
          value: "2000"
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ER_SERVER_ID
          value: "1"
        livenessProbe:
          tcpSocket:
            port: 2181
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 2181
          initialDelaySeconds: 10
          periodSeconds: 5
