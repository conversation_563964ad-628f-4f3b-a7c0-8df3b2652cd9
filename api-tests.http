### DevApp API Testing
### Use this file with REST Client extension in VS Code

### Variables
@userBaseUrl = http://localhost:8080/api/users
@orderBaseUrl = http://localhost:8081/api/orders
@auth = admin:password

### User Service Tests

# Get all users
GET {{userBaseUrl}}
Authorization: Basic {{auth}}

###

# Get user by ID
GET {{userBaseUrl}}/1
Authorization: Basic {{auth}}

###

# Create new user
POST {{userBaseUrl}}
Authorization: Basic {{auth}}
Content-Type: application/json

{
  "name": "Test User"
}

###

# Create user with validation error (empty name)
POST {{userBaseUrl}}
Authorization: Basic {{auth}}
Content-Type: application/json

{
  "name": ""
}

### Order Service Tests

# Get all orders
GET {{orderBaseUrl}}
Authorization: Basic {{auth}}

###

# Get order by ID
GET {{orderBaseUrl}}/1
Authorization: Basic {{auth}}

###

# Create new order
POST {{orderBaseUrl}}
Authorization: Basic {{auth}}
Content-Type: application/json

{
  "productId": 12345,
  "user": {
    "id": 1,
    "name": "John Doe"
  },
  "status": "PENDING"
}

###

# Create order with validation error (missing user)
POST {{orderBaseUrl}}
Authorization: Basic {{auth}}
Content-Type: application/json

{
  "productId": 12345
}

### Health Check Tests

# User service health
GET http://localhost:8080/actuator/health
Authorization: Basic {{auth}}

###

# Order service health
GET http://localhost:8081/actuator/health
Authorization: Basic {{auth}}

###

# User service info
GET http://localhost:8080/actuator/info
Authorization: Basic {{auth}}

###

# Order service info
GET http://localhost:8081/actuator/info
Authorization: Basic {{auth}}

###

# User service metrics
GET http://localhost:8080/actuator/metrics
Authorization: Basic {{auth}}

###

# Order service metrics
GET http://localhost:8081/actuator/metrics
Authorization: Basic {{auth}}

### Frontend Tests (no auth required)

# Test CORS - should work from browser
GET {{userBaseUrl}}

###

# Test CORS - should work from browser
GET {{orderBaseUrl}}
