.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.app-header {
    background-color: #343a40;
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

.app-nav {
    display: flex;
    gap: 1rem;
}

.nav-link {
    color: #adb5bd;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: white;
    background-color: rgba(255,255,255,0.1);
}

.nav-link.active {
    color: white;
    background-color: #007bff;
}

.nav-icon {
    font-size: 1.2rem;
}

.app-main {
    flex: 1;
    padding: 2rem 0;
}

.app-footer {
    background-color: #343a40;
    color: #adb5bd;
    text-align: center;
    padding: 1rem;
    margin-top: auto;
}

.app-footer p {
    margin: 0;
    font-size: 0.9rem;
}

/* Global styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Responsive design */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .app-nav {
        justify-content: center;
    }
    
    .app-main {
        padding: 1rem 0;
    }
}
