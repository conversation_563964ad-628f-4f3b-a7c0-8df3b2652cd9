{"name": "devapp-web", "version": "1.0.0", "description": "DevApp Web", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "build-prod": "ng build --configuration production", "start-prod": "ng serve --configuration production --host 0.0.0.0", "watch": "ng build --watch --configuration development", "test": "ng test --watch=false --browsers=ChromeHeadless", "test:ci": "ng test --watch=false --browsers=ChromeHeadless --code-coverage --reporters=progress,junit", "test:integration": "cypress run --config-file cypress.config.integration.js", "test:e2e": "cypress run", "lint": "ng lint", "lint:fix": "ng lint --fix", "analyze": "ng build --stats-json && npx webpack-bundle-analyzer dist/devapp-web/stats.json", "serve:prod": "npx http-server dist/devapp-web -p 4200"}, "private": true, "dependencies": {"@angular/animations": "^20.0.4", "@angular/cdk": "^20.0.3", "@angular/common": "^20.0.4", "@angular/compiler": "^20.0.4", "@angular/core": "^20.0.4", "@angular/forms": "^20.0.4", "@angular/material": "^20.0.3", "@angular/platform-browser": "^20.0.4", "@angular/platform-browser-dynamic": "^20.0.4", "@angular/router": "^20.0.4", "rxjs": "~7.8.0", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.3", "@angular/cli": "^20.0.3", "@angular/compiler-cli": "^20.0.4", "@types/jasmine": "~4.3.0", "@types/node": "^22.15.32", "cypress": "^13.10.0", "eslint": "^8.38.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^40.0.0", "eslint-plugin-prefer-arrow": "^1.2.3", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "~6.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.0.0", "karma-jasmine-html-reporter": "^2.0.0", "puppeteer": "^24.10.0", "ts-node": "~10.9.1", "tslib": "^2.8.1", "typescript": "~5.8.0"}}