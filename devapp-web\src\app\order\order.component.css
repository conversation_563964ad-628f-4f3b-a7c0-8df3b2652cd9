.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    color: #333;
    margin-bottom: 30px;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #721c24;
}

.form-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.form-field {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.form-field label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-input, .form-select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-input:disabled, .form-select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
    height: fit-content;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    margin-top: 15px;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #545b62;
}

.orders-section h2 {
    color: #333;
    margin-bottom: 15px;
}

.loading, .no-data {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.order-card {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    transition: box-shadow 0.2s;
}

.order-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.order-id {
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-shipped {
    background-color: #cce5ff;
    color: #004085;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.status-processing {
    background-color: #e2e3e5;
    color: #383d41;
}

.status-delivered {
    background-color: #d1ecf1;
    color: #0c5460;
}

.order-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item {
    color: #666;
    font-size: 14px;
}