# SonarQube Project Configuration
sonar.projectKey=devapp
sonar.projectName=DevApp
sonar.projectVersion=1.0

# Source and test directories
sonar.sources=user-app/src/main,order-app/src/main,devapp-common/src/main,devapp-web/src
sonar.tests=user-app/src/test,order-app/src/test,devapp-common/src/test,devapp-web/src

# Language-specific settings
sonar.java.source=21
sonar.java.target=21
sonar.java.binaries=**/target/classes
sonar.java.test.binaries=**/target/test-classes
sonar.java.libraries=**/target/dependency/*.jar

# JavaScript/TypeScript settings
sonar.javascript.lcov.reportPaths=devapp-web/coverage/lcov.info
sonar.typescript.lcov.reportPaths=devapp-web/coverage/lcov.info

# Coverage settings
sonar.coverage.jacoco.xmlReportPaths=**/target/site/jacoco/jacoco.xml
sonar.junit.reportPaths=**/target/surefire-reports/*.xml,**/target/failsafe-reports/*.xml

# Exclusions
sonar.exclusions=**/target/**,**/node_modules/**,**/dist/**,**/*.spec.ts,**/*.spec.js,**/test/**,**/*Test.java,**/*Tests.java

# Test exclusions
sonar.test.exclusions=**/target/**,**/node_modules/**,**/dist/**

# Duplication exclusions
sonar.cpd.exclusions=**/target/**,**/node_modules/**,**/dist/**

# Quality gate settings
sonar.qualitygate.wait=true

# Additional settings
sonar.sourceEncoding=UTF-8
sonar.scm.provider=git
sonar.scm.forceReloadAll=true

# Security hotspot settings
sonar.security.hotspots.inheritFromParent=true

# Issue assignment
sonar.issues.defaultAssigneeLogin=admin

# Branch analysis (for SonarQube Developer Edition and above)
# sonar.branch.name=${BRANCH_NAME}
# sonar.branch.target=main
