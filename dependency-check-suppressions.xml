<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    <!-- 
    This file contains suppressions for OWASP Dependency Check.
    Add suppressions for known false positives or accepted risks.
    
    Example suppression:
    <suppress>
        <notes><![CDATA[
        This is a false positive for our use case because...
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-core@.*$</packageUrl>
        <cve>CVE-2022-22965</cve>
    </suppress>
    -->
    
    <!-- Suppress false positives for test dependencies -->
    <suppress>
        <notes><![CDATA[
        Test dependencies are not included in production builds
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/.*@.*$</packageUrl>
        <cve>CVE-2023-20863</cve>
        <scope>test</scope>
    </suppress>
    
    <!-- Suppress known issues in development dependencies -->
    <suppress>
        <notes><![CDATA[
        Development-only dependency, not exposed in production
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-devtools@.*$</packageUrl>
        <vulnerabilityName regex="true">.*</vulnerabilityName>
    </suppress>
    
    <!-- Example: Suppress specific CVE for a specific version -->
    <!--
    <suppress>
        <notes><![CDATA[
        This vulnerability does not affect our usage pattern
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-databind@2\.13\..*$</packageUrl>
        <cve>CVE-2022-42003</cve>
    </suppress>
    -->
</suppressions>
